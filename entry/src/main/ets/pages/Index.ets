import { router } from '@kit.ArkUI';
import MessageChannel from '../flutter/MessageChannel';

@Entry
@Component
struct Index {

  @State message: string = 'Hello World111';

   channel: MessageChannel = new MessageChannel();


  build() {
    Column(){
      Blank().height(40)
      Text(this.message)
        .id('HelloWorld1111')
        .fontWeight(FontWeight.Bold)
        .alignRules({
          center: { anchor: '__container__', align: VerticalAlign.Center },
          middle: { anchor: '__container__', align: HorizontalAlign.Center }
        })
        .onClick(() => {
          this.message = 'Welcome';
          router.pushUrl({url: 'pages/FlutterPageContainer'})
        })

      Text("调用Flutter --> ")
        .fontWeight(FontWeight.Bold)
        .onClick(() => {
          // 调用Flutter
          let r = this.channel.invokeFlutter("getFlutterTestValue");
          console.info(` 调用 Flutter $r`)
        })
    }.margin({
      left:20 ,right:20,top:10, bottom:20
    }).align(Alignment.Center)

  }
}