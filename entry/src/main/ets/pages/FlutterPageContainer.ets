import { FlutterEntry, FlutterPage, FlutterView } from '@ohos/flutter_ohos';

@Entry
@Component
struct FlutterPageContainer {
  @State message: string = 'Hello World111';


  private flutterEntry?: FlutterEntry;
  private flutterView?: FlutterView;

  aboutToAppear(): void {
    this.flutterEntry = new FlutterEntry(getContext(this));
    this.flutterEntry.aboutToAppear();
    this.flutterView = this.flutterEntry.getFlutterView();
  }

  aboutToDisappear() {
    this.flutterEntry?.aboutToDisappear();
  }

  onPageShow() {
    this.flutterEntry?.onPageShow();
  }

  onPageHide() {
    this.flutterEntry?.onPageHide();
  }

  build() {
    RelativeContainer() {
      FlutterPage({ viewId: this.flutterView?.getId()})
    }
  }

}