{"app": {"products": [{"name": "default", "signingConfig": "default", "targetSdkVersion": "5.1.0(18)", "compatibleSdkVersion": "5.1.0(18)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}}}], "buildModeSet": [{"name": "debug"}, {"name": "release"}], "signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"certpath": "/Users/<USER>/.ohos/config/default_ohos_app_iAFY_TZMzKa7Sxm65RFxEBbwcP7LDWjim5YnGnlSraE=.cer", "keyAlias": "debugKey", "keyPassword": "0000001A14DD9B4D804765B67F5C8BE9D16D8E9CD4143824C695FA89A6434B1DDC008C4505B62444BD84", "profile": "/Users/<USER>/.ohos/config/default_ohos_app_iAFY_TZMzKa7Sxm65RFxEBbwcP7LDWjim5YnGnlSraE=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "/Users/<USER>/.ohos/config/default_ohos_app_iAFY_TZMzKa7Sxm65RFxEBbwcP7LDWjim5YnGnlSraE=.p12", "storePassword": "0000001A9357BD63F91CE5F3DCAFBD261466B917692A3FC287F157EA4592E13240F84D875EE9DD2960AD"}}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}, {"name": "flutter_module", "srcPath": "./flutter_module"}]}