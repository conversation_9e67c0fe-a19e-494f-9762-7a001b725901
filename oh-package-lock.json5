{"meta": {"stableOrder": true, "enableUnifiedLockfile": false}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@ohos/flutter_module@flutter_module": "@ohos/flutter_module@flutter_module", "@ohos/flutter_ohos@flutter_module/har/flutter.har": "@ohos/flutter_ohos@har/flutter.har", "@ohos/flutter_ohos@har/flutter.har": "@ohos/flutter_ohos@har/flutter.har", "@ohos/hamock@1.0.0": "@ohos/hamock@1.0.0", "@ohos/hypium@1.0.21": "@ohos/hypium@1.0.21"}, "packages": {"@ohos/flutter_module@flutter_module": {"name": "@ohos/flutter_module", "version": "1.0.0", "resolved": "flutter_module", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}}, "@ohos/flutter_ohos@har/flutter.har": {"name": "@ohos/flutter_ohos", "version": "1.0.0-9e5c99f0c5", "resolved": "har/flutter.har", "registryType": "local"}, "@ohos/hamock@1.0.0": {"name": "@ohos/hamock", "version": "1.0.0", "integrity": "sha512-K6lDPYc6VkKe6ZBNQa9aoG+ZZMiwqfcR/7yAVFSUGIuOAhPvCJAo9+t1fZnpe0dBRBPxj2bxPPbKh69VuyAtDg==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/hamock/-/hamock-1.0.0.har", "registryType": "ohpm"}, "@ohos/hypium@1.0.21": {"name": "@ohos/hypium", "version": "1.0.21", "integrity": "sha512-iyKGMXxE+9PpCkqEwu0VykN/7hNpb+QOeIuHwkmZnxOpI+dFZt6yhPB7k89EgV1MiSK/ieV/hMjr5Z2mWwRfMQ==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/hypium/-/hypium-1.0.21.har", "registryType": "ohpm"}}}